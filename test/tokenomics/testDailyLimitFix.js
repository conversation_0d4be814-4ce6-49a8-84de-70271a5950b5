#!/usr/bin/env node

/**
 * Test to verify that daily limits are properly enforced
 * This test should show NO rewards being sent after daily limits are reached
 */

import { UserSimulator } from './UserSimulator.js';
import { ethers } from 'ethers';

async function testDailyLimitFix() {
    console.log('🧪 Testing Daily Limit Fix');
    console.log('===========================');
    
    // Create a mock account
    const mockAccount = {
        address: '******************************************',
        privateKey: '0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80'
    };
    
    const config = {
        apiBaseUrl: 'http://localhost:3001/api',
        hardhatUrl: 'http://localhost:8545',
        chainId: 31337,
        hotWalletAddress: '******************************************'
    };

    try {
        // Create a grinder user simulator
        const grinder = new UserSimulator({
            type: 'grinder',
            account: mockAccount,
            config: config,
            id: 'test_grinder_1',
            transactionTracker: null,
            treasuryMonitor: null,
            globalRateLimiter: null
        });

        console.log('🎯 Testing level completion with daily limits...');
        
        // Test multiple level completions to trigger daily limit
        for (let level = 1; level <= 15; level++) {
            console.log(`\n--- Testing Level ${level} ---`);
            
            const completionData = {
                completed: true,
                levelNumber: level,
                totalEnemies: 50,
                enemiesDefeated: 50 // Perfect completion
            };

            const levelConfig = {
                totalEnemies: 50
            };

            // Use REAL reward calculation
            const rewardData = grinder.tokenEconomyManager.calculateLevelReward(completionData, levelConfig);
            
            console.log(`Reward calculation result: ${rewardData.totalReward} WISH`);
            console.log(`Reason: ${rewardData.breakdown.reason || 'valid'}`);
            
            if (rewardData.totalReward > 0) {
                console.log(`✅ Level ${level}: Should award ${rewardData.totalReward} WISH`);
                
                // In a real test, we would call awardTokens here
                // For this test, we just simulate the recording
                grinder.tokenEconomyManager.dailyRewardTracker.recordLevelCompletion(
                    level,
                    rewardData.breakdown.completionPercentage,
                    rewardData.totalReward
                );
            } else {
                console.log(`🚫 Level ${level}: No reward (${rewardData.breakdown.reason})`);
                console.log(`   ⚠️ This should NOT trigger any API calls or ETH transfers!`);
            }
            
            // Check daily progress
            const progress = grinder.tokenEconomyManager.dailyRewardTracker.getDailyProgress();
            console.log(`Daily progress: ${progress.dailyTokensEarned}/${progress.maxDailyTokens} tokens`);
            
            if (progress.isLimitReached) {
                console.log(`🛑 Daily limit reached! No more rewards should be awarded.`);
            }
        }
        
        console.log('\n✅ Daily limit test completed successfully!');
        console.log('📋 Summary: Levels with no rewards should NOT trigger API calls.');
        
        return true;
    } catch (error) {
        console.error('❌ Test failed:', error);
        return false;
    }
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    testDailyLimitFix()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('❌ Unexpected error:', error);
            process.exit(1);
        });
}

export { testDailyLimitFix };
