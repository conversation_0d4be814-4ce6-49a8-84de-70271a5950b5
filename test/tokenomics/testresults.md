npm run test:tokenomics

> WarpSector-game@1.0.0 test:tokenomics
> node test/tokenomics/runStressTest.js

🚀 Starting WarpSector Tokenomics Stress Test
============================================================
📋 Test Configuration:
   Duration: 300 seconds
   Max Users: 10
   API URL: http://localhost:3001/api
   Hardhat URL: http://localhost:8545
   Hot Wallet: ******************************************
   Scenario: All scenarios
   Verbose: Disabled

🔍 Validating prerequisites...
   ✅ Game Server: OK
   ✅ Hardhat Node: OK
   ✅ Hot Wallet Balance: OK
   ✅ Test Accounts: OK
✅ All prerequisites validated

🧪 TokenomicsStressTest initialized
📊 Configuration: {
  apiBaseUrl: 'http://localhost:3001/api',
  hardhatUrl: 'http://localhost:8545',
  chainId: 31337,
  hotWalletAddress: '******************************************',
  testDuration: 300000,
  maxConcurrentUsers: 10,
  verbose: false,
  reportFile: null,
  scenario: null
}
🚀 Initializing Tokenomics Stress Test Framework...
✅ Server health check passed: { status: 'OK', message: 'AI Service Server is running' }
🔗 Connected to network: Network {}
💰 Hot wallet balance: 100.0 ETH
👥 Found 20 test accounts
📊 TransactionTracker initialized
💰 TreasuryMonitor initialized
📊 Monitoring systems initialized
✅ Initialized daily reward tracker for user: ******************************************
🧪 grinder_1: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
🔧 grinder_1: Node.js compatibility setup complete
👤 Created grinder user simulator: grinder_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 grinder_2: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
🔧 grinder_2: Node.js compatibility setup complete
👤 Created grinder user simulator: grinder_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 grinder_3: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
🔧 grinder_3: Node.js compatibility setup complete
👤 Created grinder user simulator: grinder_3 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 whale_1: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
🔧 whale_1: Node.js compatibility setup complete
👤 Created whale user simulator: whale_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 whale_2: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
🔧 whale_2: Node.js compatibility setup complete
👤 Created whale user simulator: whale_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 creator_1: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
🔧 creator_1: Node.js compatibility setup complete
👤 Created creator user simulator: creator_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 creator_2: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
🔧 creator_2: Node.js compatibility setup complete
👤 Created creator user simulator: creator_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 casual_1: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
🔧 casual_1: Node.js compatibility setup complete
👤 Created casual user simulator: casual_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 casual_2: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
🔧 casual_2: Node.js compatibility setup complete
👤 Created casual user simulator: casual_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 casual_3: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
🔧 casual_3: Node.js compatibility setup complete
👤 Created casual user simulator: casual_3 (******************************************)
👥 Created 10 user simulators
✅ Stress test framework initialized successfully
🚀 Starting Tokenomics Stress Test...
💰 Initial treasury balance: 100 ETH
🚀 TreasuryMonitor started
🚀 TransactionTracker started
📊 Monitoring started
🔍 Queue health monitoring started
🎮 Running Sequential Grinding Test (Attack Vector 1)...
📋 Objective: Test if grinder earnings can drain treasury
👥 Testing with 3 grinder accounts
🎨 Running Creator Reward Test (Attack Vector 2)...
📋 Objective: Test creator reward sustainability and 50% distribution accuracy
👨‍🎨 Testing with 2 creators and 2 whales
🎨 Phase 1: Environment Creation
🎨 creator_1 creating environment: "Ice planet with aurora borealis"
💸 creator_1 spending 2500 ETH for: Reality Warp: Ice planet with aurora borealis
💸 creator_1 sending 2500 ETH to hot wallet for: Reality Warp: Ice planet with aurora borealis
👥 Running Multi-Account Coordination Test (Attack Vector 3)...
📋 Objective: Test economic balance under coordinated behavior
🤝 Simulating coordinated behavior across 5 accounts
⚡ Starting coordinated actions...
💰 Running Treasury Drain Test (Maximum Stress)...
📋 Objective: Test maximum stress on treasury with all users acting simultaneously
💰 Initial treasury balance: 100 ETH
🎯 Starting grinder session: grinder_1
⏳ Grinder grinder_1 waiting 1500ms to prevent server overload...
💰 Pre-stress treasury balance: 100 ETH
⚡ Initiating maximum stress scenario...
🔢 Next nonce for creator_1: 0
✅ REAL blockchain transaction mined: 0xbc052ae0148bb27663cf3fde061a428600c8a2ef08cf2f3cdf9dfe321f7ffecf
⛽ Gas used: 21000
💸 creator_1 completed REAL ETH transfer: 2500 ETH to hot wallet
⚡ grinder_1 starting maximum stress behavior
⏳ Grinder grinder_1 waiting 1500ms to prevent server overload...
🤝 grinder_1 starting coordinated behavior simulation
🎮 Starting grinder session for grinder_1...
🔐 Authenticated grinder_1 with server
⏳ Grinder grinder_1 waiting 1500ms to prevent server overload...
⚡ grinder_2 starting maximum stress behavior
⏳ Grinder grinder_2 waiting 3000ms to prevent server overload...
✅ API Call: POST /generate-environment (141ms)
🎨 Environment Creation Tracked: Ice planet with Environment by creator_1
✅ Environment created by creator_1: Ice planet with Environment
✅ Environment created: Ice planet with Environment by creator_1
✅ Environment created: Ice planet with Environment by creator_1
🎨 creator_2 creating environment: "Desert oasis with sandstorms"
💸 creator_2 spending 2500 ETH for: Reality Warp: Desert oasis with sandstorms
💸 creator_2 sending 2500 ETH to hot wallet for: Reality Warp: Desert oasis with sandstorms
🔢 Next nonce for creator_2: 0
⚡ grinder_3 starting maximum stress behavior
⏳ Grinder grinder_3 waiting 4500ms to prevent server overload...
✅ REAL blockchain transaction mined: 0x9f9a8649bac0e5dd04fa311852491cb95bc22335c1a034034d2475db6928465c
⛽ Gas used: 21000
💸 creator_2 completed REAL ETH transfer: 2500 ETH to hot wallet
🤝 grinder_2 starting coordinated behavior simulation
🎮 Starting grinder session for grinder_2...
🔐 Authenticated grinder_2 with server
⏳ Grinder grinder_2 waiting 3000ms to prevent server overload...
⚡ whale_1 starting maximum stress behavior
⚡ whale_2 starting maximum stress behavior
✅ API Call: POST /generate-environment (118ms)
🎨 Environment Creation Tracked: Desert oasis with Environment by creator_2
✅ Environment created by creator_2: Desert oasis with Environment
✅ Environment created: Desert oasis with Environment by creator_2
✅ Environment created: Desert oasis with Environment by creator_2
💰 Phase 2: Mystical Environment Purchases
🐋 whale_1 purchasing Mystical Environment for 25000 tokens
🎁 Expected creator reward: 12500 tokens
💰 whale_1 purchasing Mystical Environment for 1000 ETH
🎁 Expected creator reward: 500 ETH
⚡ creator_1 starting maximum stress behavior
🎨 creator_1 creating environment: "Underwater coral reef with bioluminescence"
💸 creator_1 spending 2500 ETH for: Reality Warp: Underwater coral reef with bioluminescence
💸 creator_1 sending 2500 ETH to hot wallet for: Reality Warp: Underwater coral reef with bioluminescence
🔢 Next nonce for creator_1: 1
🤝 grinder_3 starting coordinated behavior simulation
🎮 Starting grinder session for grinder_3...
🔐 Authenticated grinder_3 with server
⏳ Grinder grinder_3 waiting 4500ms to prevent server overload...
⚡ creator_2 starting maximum stress behavior
🎨 creator_2 creating environment: "Space station with nebula backdrop"
💸 creator_2 spending 2500 ETH for: Reality Warp: Space station with nebula backdrop
💸 creator_2 sending 2500 ETH to hot wallet for: Reality Warp: Space station with nebula backdrop
✅ REAL blockchain transaction mined: 0x7806b830c14b82586788c1a6dba9c010f30f011ae6a7e626fcf9939270397fef
⛽ Gas used: 21000
💸 creator_1 completed REAL ETH transfer: 2500 ETH to hot wallet
🔢 Next nonce for creator_2: 1
✅ REAL blockchain transaction mined: 0xe2e68df4c95a35f2ec83dc98fb0a1078e5b2a4d02967b8397982fe6fff42ea08
⛽ Gas used: 21000
💸 creator_2 completed REAL ETH transfer: 2500 ETH to hot wallet
⚡ casual_1 starting maximum stress behavior
✅ REAL Mystical Environment purchased: Test Environment for 1000 ETH
🎁 Creator reward distributed: 500 ETH to user_1756654556315_5s6n73ksu
🤝 whale_1 starting coordinated behavior simulation
🎮 Starting whale session for whale_1...
🔐 Authenticated whale_1 with server
🛒 Extra Life: 1500 ETH (ETH test mode)
💸 whale_1 sending 1500 ETH to hot wallet for: Extra Life
✅ API Call: POST /generate-environment (121ms)
🎨 Environment Creation Tracked: Underwater coral reef Environment by creator_1
✅ Environment created by creator_1: Underwater coral reef Environment
✅ Environment created: Underwater coral reef Environment by creator_1
🔢 Next nonce for whale_1: 0
⚡ casual_2 starting maximum stress behavior
✅ API Call: POST /generate-environment (99ms)
🎨 Environment Creation Tracked: Space station with Environment by creator_2
✅ Environment created by creator_2: Space station with Environment
✅ Environment created: Space station with Environment by creator_2
✅ REAL blockchain transaction mined: 0x7a4c66cb8e56f1715969c500dcd28f5d6a4ee09cc60cd8db5bd62caf44322190
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 1500 ETH to hot wallet
⏳ Waiting 387.09624591880754ms before next transaction...
⚡ casual_3 starting maximum stress behavior
🤝 whale_2 starting coordinated behavior simulation
🎮 Starting whale session for whale_2...
🔐 Authenticated whale_2 with server
🛒 Extra Life: 1500 ETH (ETH test mode)
💸 whale_2 sending 1500 ETH to hot wallet for: Extra Life
🔢 Next nonce for whale_2: 0
✅ REAL blockchain transaction mined: 0xbaadd156d08bc3e6cd7363b245f3c8df2eef005f01004488162ecc2c1d646080
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 1500 ETH to hot wallet
⏳ Waiting 389.284421099302ms before next transaction...
🐋 whale_2 purchasing Mystical Environment for 25000 tokens
🎁 Expected creator reward: 12500 tokens
💰 whale_2 purchasing Mystical Environment for 1000 ETH
🎁 Expected creator reward: 500 ETH
🛒 Extra Wingman: 1000 ETH (ETH test mode)
💸 whale_1 sending 1000 ETH to hot wallet for: Extra Wingman
🔢 Next nonce for whale_1: 1
🎮 Level 1: Simulating completion...
✅ Level 1 completed! JACKPOT: NaN tokens
🚫 Level 1: No reward (game decision)
🎮 Level 1: Simulating completion...
🚫 Level 1: No reward (level_already_completed_today)
✅ REAL blockchain transaction mined: 0x225730f0d9b7eb0e2d538d83482a86b1d7525701ce25e6622d92d8edeb0a7ae4
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 1000 ETH to hot wallet
⏳ Waiting 534.7597452303028ms before next transaction...
✅ REAL Mystical Environment purchased: Test Environment for 1000 ETH
🎁 Creator reward distributed: 500 ETH to user_1756654556315_5s6n73ksu
🛒 Extra Wingman: 1000 ETH (ETH test mode)
💸 whale_2 sending 1000 ETH to hot wallet for: Extra Wingman
🎮 Level 1: Simulating completion...
🚫 Level 1: No reward (level_already_completed_today)
🔢 Next nonce for whale_2: 1
✅ REAL blockchain transaction mined: 0x0a47db681348e08300e62cc77b78c7f66c09f3a02284df20c54986e95e936f3a
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 1000 ETH to hot wallet
⏳ Waiting 592.5735017865815ms before next transaction...
🛒 Spread Ammo: 750 ETH (ETH test mode)
💸 whale_1 sending 750 ETH to hot wallet for: Spread Ammo
📊 Creator Reward Test Results:
   Total Purchases: 50000 tokens
   Expected Creator Rewards: 25000 tokens (50%)
   Environments Created: 2
   Creator Reward Accuracy: Testing 50% distribution...
🔢 Next nonce for whale_1: 2
✅ REAL blockchain transaction mined: 0xdeb4de1a4a88349b07a05cd7a9c274ed214adfc6c7c11d76ce0edbbd2a8cd4f0
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 750 ETH to hot wallet
⏳ Waiting 705.2249943711315ms before next transaction...
🛒 Spread Ammo: 750 ETH (ETH test mode)
💸 whale_2 sending 750 ETH to hot wallet for: Spread Ammo
🔢 Next nonce for whale_2: 2
✅ REAL blockchain transaction mined: 0xb1568bdd2091a02eca0ac5045a50196176b14a324478ca7031b94350dfbda77d
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 750 ETH to hot wallet
⏳ Waiting 754.4192125462166ms before next transaction...
🎮 Level 2: Simulating completion...
✅ Level 2 completed! PARTIAL: NaN tokens
🚫 Level 2: No reward (game decision)
🎮 Level 2: Simulating completion...
🚫 Level 2: No reward (level_already_completed_today)
🎮 Level 1: Simulating minimal effort completion...
✅ Level 1 completed! PARTIAL: 543 tokens
🎁 Game awarded 543 WISH → 54.300000000000004 ETH
💰 Sending 54.300000000000004 ETH to whale_1 for: Level 1 completion - minimal effort
🎮 Level 2: Simulating completion...
🚫 Level 2: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (178ms)
✅ REAL ETH transfer completed: 54.300000000000004 ETH to ******************************************
📤 Transaction hash: 0xe168df830dded7b5f35fd43f9a092aea212e905e86430ff7f2a7b2f3aafff6f4
🎮 Level 1: Simulating completion...
✅ Level 1 completed! PARTIAL: 813 tokens
🎁 Game awarded 813 WISH → 81.30000000000001 ETH
💰 Sending 81.30000000000001 ETH to grinder_2 for: Level 1 completion - game awarded 813 WISH
🎮 Level 1: Simulating minimal effort completion...
✅ Level 1 completed! PARTIAL: 879 tokens
🎁 Game awarded 879 WISH → 87.9 ETH
💰 Sending 87.9 ETH to whale_2 for: Level 1 completion - minimal effort
✅ API Call: POST /wallet/send (185ms)
✅ REAL ETH transfer completed: 81.30000000000001 ETH to ******************************************
📤 Transaction hash: 0xa716864154aacee6a8c1832b8ccf996e1992ac2475c6e8fd90e671ecc6d6aecf
✅ API Call: POST /wallet/send (175ms)
✅ REAL ETH transfer completed: 87.9 ETH to ******************************************
📤 Transaction hash: 0xe9754e2d6bf66d18f996e22bcad44f7a5fb6b13847eb991a4a5ec34c164efbff
🎮 Level 1: Simulating completion...
🚫 Level 1: No reward (level_already_completed_today)
🎮 Level 3: Simulating completion...
✅ Level 3 completed! PARTIAL: NaN tokens
🚫 Level 3: No reward (game decision)
🎮 Level 3: Simulating completion...
🚫 Level 3: No reward (level_already_completed_today)
🎮 Level 2: Simulating minimal effort completion...
✅ Level 2 completed! PARTIAL: 791 tokens
🎁 Game awarded 791 WISH → 79.10000000000001 ETH
💰 Sending 79.10000000000001 ETH to whale_1 for: Level 2 completion - minimal effort
🎮 Level 3: Simulating completion...
🚫 Level 3: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (177ms)
✅ REAL ETH transfer completed: 79.10000000000001 ETH to ******************************************
📤 Transaction hash: 0x59a9ff14b1b5f72cf666bf9c1f88cd50769cf2831a50bc2004f3948c88900d0d
🎮 Level 2: Simulating minimal effort completion...
✅ Level 2 completed! PARTIAL: 913 tokens
🎁 Game awarded 913 WISH → 91.30000000000001 ETH
💰 Sending 91.30000000000001 ETH to whale_2 for: Level 2 completion - minimal effort
✅ API Call: POST /wallet/send (167ms)
✅ REAL ETH transfer completed: 91.30000000000001 ETH to ******************************************
📤 Transaction hash: 0x61e988dc8902b8b3e75e4ca8318fc91035a93d9a20f131c4e381e6dbbdc1ee06
🎮 Level 2: Simulating completion...
✅ Level 2 completed! PARTIAL: 632 tokens
🎁 Game awarded 632 WISH → 63.2 ETH
💰 Sending 63.2 ETH to grinder_2 for: Level 2 completion - game awarded 632 WISH
✅ API Call: POST /wallet/send (146ms)
✅ REAL ETH transfer completed: 63.2 ETH to ******************************************
📤 Transaction hash: 0x317ba17faf42fda8abc22f791331a3ffb71e26dab168fee9c6ac0c2d005ba0cc
🎮 Level 2: Simulating completion...
🚫 Level 2: No reward (level_already_completed_today)
🎮 Level 1: Simulating completion...
✅ Level 1 completed! PARTIAL: 576 tokens
🎁 Game awarded 576 WISH → 57.6 ETH
💰 Sending 57.6 ETH to grinder_3 for: Level 1 completion - game awarded 576 WISH
💰 Balance change: +14985.299774 ETH (15085.299774 ETH total)
📈 Treasury inflow detected: +14985.299774 ETH
⚠️ ALERT: Extremely large balance change detected: 14985.299774 ETH
✅ API Call: POST /wallet/send (164ms)
✅ REAL ETH transfer completed: 57.6 ETH to ******************************************
📤 Transaction hash: 0x5cf6a0a7dbbeaca04698856efb0757e5045c79fc2e4e38bdbd2627d15ac7dc2e
🎮 Level 1: Simulating completion...
🚫 Level 1: No reward (level_already_completed_today)
💰 Whale whale_1 completed session with heavy spending
✅ Completed whale session for whale_1
🎮 Level 4: Simulating completion...
✅ Level 4 completed! PARTIAL: NaN tokens
🚫 Level 4: No reward (game decision)
🎮 Level 4: Simulating completion...
🚫 Level 4: No reward (level_already_completed_today)
🎮 Level 4: Simulating completion...
🚫 Level 4: No reward (level_already_completed_today)
💰 Whale whale_2 completed session with heavy spending
✅ Completed whale session for whale_2
🎮 Level 3: Simulating completion...
✅ Level 3 completed! PARTIAL: 558 tokens
🎁 Game awarded 558 WISH → 55.800000000000004 ETH
💰 Sending 55.800000000000004 ETH to grinder_2 for: Level 3 completion - game awarded 558 WISH
✅ API Call: POST /wallet/send (152ms)
✅ REAL ETH transfer completed: 55.800000000000004 ETH to ******************************************
📤 Transaction hash: 0x100a7f7564c233e79158b4747c1a0a99fc742571ce19d92b30b3404508236c65
🎮 Level 2: Simulating completion...
✅ Level 2 completed! PARTIAL: 651 tokens
🎁 Game awarded 651 WISH → 65.10000000000001 ETH
💰 Sending 65.10000000000001 ETH to grinder_3 for: Level 2 completion - game awarded 651 WISH
🎮 Level 3: Simulating completion...
🚫 Level 3: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (165ms)
✅ REAL ETH transfer completed: 65.10000000000001 ETH to ******************************************
📤 Transaction hash: 0x475dc77721fb867631ced886f0ea9303d91892c9798083c6798aec0c3731618b
🎮 Level 2: Simulating completion...
🚫 Level 2: No reward (level_already_completed_today)
🎮 Level 5: Simulating completion...
✅ Level 5 completed! PARTIAL: NaN tokens
🚫 Level 5: No reward (game decision)
🎮 Level 5: Simulating completion...
🚫 Level 5: No reward (level_already_completed_today)
🎮 Level 5: Simulating completion...
🚫 Level 5: No reward (level_already_completed_today)
🎮 Level 4: Simulating completion...
✅ Level 4 completed! PARTIAL: 750 tokens
🎁 Game awarded 750 WISH → 75 ETH
💰 Sending 75 ETH to grinder_2 for: Level 4 completion - game awarded 750 WISH
🎮 Level 3: Simulating completion...
✅ Level 3 completed! PARTIAL: 941 tokens
🎁 Game awarded 941 WISH → 94.10000000000001 ETH
💰 Sending 94.10000000000001 ETH to grinder_3 for: Level 3 completion - game awarded 941 WISH
🎮 Level 3: Simulating completion...
🚫 Level 3: No reward (level_already_completed_today)
🎮 Level 4: Simulating completion...
🚫 Level 4: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (185ms)
✅ REAL ETH transfer completed: 75 ETH to ******************************************
📤 Transaction hash: 0xe4d408d5fc687f0f679dfa804e9f514f9d0ee3d7a6d78e50585855702ff01580
✅ API Call: POST /wallet/send (170ms)
✅ REAL ETH transfer completed: 94.10000000000001 ETH to ******************************************
📤 Transaction hash: 0x1b3d60d162b412fbf7849d32c5fba281bd6afa97173b085d809dfaea6060fe09
🎮 Level 6: Simulating completion...
✅ Level 6 completed! PARTIAL: NaN tokens
🚫 Level 6: No reward (game decision)
🎮 Level 6: Simulating completion...
🚫 Level 6: No reward (level_already_completed_today)
🎮 Level 6: Simulating completion...
🚫 Level 6: No reward (level_already_completed_today)
🎮 Level 4: Simulating completion...
✅ Level 4 completed! PARTIAL: 521 tokens
🎁 Game awarded 521 WISH → 52.1 ETH
💰 Sending 52.1 ETH to grinder_3 for: Level 4 completion - game awarded 521 WISH
🎮 Level 5: Simulating completion...
✅ Level 5 completed! PARTIAL: 1269 tokens
🎁 Game awarded 1269 WISH → 126.9 ETH
💰 Sending 126.9 ETH to grinder_2 for: Level 5 completion - game awarded 1269 WISH
🎮 Level 4: Simulating completion...
🚫 Level 4: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (211ms)
✅ REAL ETH transfer completed: 52.1 ETH to ******************************************
📤 Transaction hash: 0x147b4adee146ce20a25cc7b29c97fef32e74cb9b84a3cdace4c52f931af45e58
✅ API Call: POST /wallet/send (207ms)
✅ REAL ETH transfer completed: 126.9 ETH to ******************************************
📤 Transaction hash: 0x7d5a3a836ed18f7eece6e663f5936c90a71892cf511c2ce144ee7af99e5d3836
🎮 Level 5: Simulating completion...
🚫 Level 5: No reward (level_already_completed_today)
🎮 Level 7: Simulating completion...
✅ Level 7 completed! PARTIAL: NaN tokens
🚫 Level 7: No reward (game decision)
🎮 Level 7: Simulating completion...
🚫 Level 7: No reward (level_already_completed_today)
🎮 Level 7: Simulating completion...
🚫 Level 7: No reward (level_already_completed_today)
💰 Balance change: -469.000132 ETH (14616.299642 ETH total)
📉 Treasury outflow detected: -469.000132 ETH
🎮 Level 5: Simulating completion...
✅ Level 5 completed! PARTIAL: 1309 tokens
🎁 Game awarded 1309 WISH → 130.9 ETH
💰 Sending 130.9 ETH to grinder_3 for: Level 5 completion - game awarded 1309 WISH
🎮 Level 5: Simulating completion...
🚫 Level 5: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (167ms)
✅ REAL ETH transfer completed: 130.9 ETH to ******************************************
📤 Transaction hash: 0xc5e457f434638eb3a594cdc475255d8ff16cbdeaca928c2ef4cd9318d045ddfd
🎮 Level 6: Simulating completion...
✅ Level 6 completed! PARTIAL: 1023 tokens
🎁 Game awarded 1023 WISH → 102.30000000000001 ETH
💰 Sending 102.30000000000001 ETH to grinder_2 for: Level 6 completion - game awarded 1023 WISH
🎮 Level 6: Simulating completion...
🚫 Level 6: No reward (level_already_completed_today)
🎮 Level 8: Simulating completion...
✅ Level 8 completed! PARTIAL: NaN tokens
🚫 Level 8: No reward (game decision)
✅ API Call: POST /wallet/send (142ms)
✅ REAL ETH transfer completed: 102.30000000000001 ETH to ******************************************
📤 Transaction hash: 0xfde8bee24db4328fef6ec67e4170beaefab0221cd303613660e101dab97b73d8
🎮 Level 8: Simulating completion...
🚫 Level 8: No reward (level_already_completed_today)
🎮 Level 8: Simulating completion...
🚫 Level 8: No reward (level_already_completed_today)
🎮 Level 6: Simulating completion...
✅ Level 6 completed! PARTIAL: 1543 tokens
🎁 Game awarded 1543 WISH → 154.3 ETH
💰 Sending 154.3 ETH to grinder_3 for: Level 6 completion - game awarded 1543 WISH
🎮 Level 9: Simulating completion...
✅ Level 9 completed! PARTIAL: NaN tokens
🚫 Level 9: No reward (game decision)
🎮 Level 6: Simulating completion...
🚫 Level 6: No reward (level_already_completed_today)
🎮 Level 7: Simulating completion...
✅ Level 7 completed! PARTIAL: 1669 tokens
🎁 Game awarded 1669 WISH → 166.9 ETH
💰 Sending 166.9 ETH to grinder_2 for: Level 7 completion - game awarded 1669 WISH
✅ API Call: POST /wallet/send (181ms)
✅ REAL ETH transfer completed: 154.3 ETH to ******************************************
📤 Transaction hash: 0x4bd3ce6cd57591bd71efa551f1fe8b7c25817b0f655c073619da116a8d30e8e3
✅ API Call: POST /wallet/send (133ms)
✅ REAL ETH transfer completed: 166.9 ETH to ******************************************
📤 Transaction hash: 0x243526f2f7ebf18013499ac07926981c4f3a5278bbfd8eae1f0b8effe70b8627
🎮 Level 7: Simulating completion...
🚫 Level 7: No reward (level_already_completed_today)
🎮 Level 9: Simulating completion...
🚫 Level 9: No reward (level_already_completed_today)
🎮 Level 9: Simulating completion...
🚫 Level 9: No reward (level_already_completed_today)
🎮 Level 7: Simulating completion...
✅ Level 7 completed! PARTIAL: 1196 tokens
🎁 Game awarded 1196 WISH → 119.60000000000001 ETH
💰 Sending 119.60000000000001 ETH to grinder_3 for: Level 7 completion - game awarded 1196 WISH
🎮 Level 10: Simulating completion...
✅ Level 10 completed! PARTIAL: NaN tokens
🚫 Level 10: No reward (game decision)
✅ API Call: POST /wallet/send (156ms)
✅ REAL ETH transfer completed: 119.60000000000001 ETH to ******************************************
📤 Transaction hash: 0xd32370c70ba3626ad171499c96a5b5d080f2262077bd31770587a298246d526b
🎮 Level 7: Simulating completion...
🚫 Level 7: No reward (level_already_completed_today)
🎮 Level 10: Simulating completion...
🚫 Level 10: No reward (level_already_completed_today)
🎮 Level 8: Simulating completion...
✅ Level 8 completed! PARTIAL: 1044 tokens
🎁 Game awarded 1044 WISH → 104.4 ETH
💰 Sending 104.4 ETH to grinder_2 for: Level 8 completion - game awarded 1044 WISH
🎮 Level 8: Simulating completion...
🚫 Level 8: No reward (level_already_completed_today)
🎮 Level 10: Simulating completion...
🚫 Level 10: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (160ms)
✅ REAL ETH transfer completed: 104.4 ETH to ******************************************
📤 Transaction hash: 0x413b663ccb88cd217b2889b28860f07b65cf5712d23459a41cad075f7a367b90
🏆 Grinder grinder_1 completed 10 levels with REAL rewards
✅ Completed grinder session for grinder_1
🎮 Level 8: Simulating completion...
✅ Level 8 completed! PARTIAL: 1877 tokens
🎁 Game awarded 1877 WISH → 187.70000000000002 ETH
💰 Sending 187.70000000000002 ETH to grinder_3 for: Level 8 completion - game awarded 1877 WISH
🎮 Level 8: Simulating completion...
🚫 Level 8: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (158ms)
✅ REAL ETH transfer completed: 187.70000000000002 ETH to ******************************************
📤 Transaction hash: 0x2176678f8496c32bdf974e613b89a00f85f90d7bcd5426665a5b15f8b164d745
🏆 Grinder grinder_1 completed 10 levels with REAL rewards
📊 Treasury impact after grinder_1: -13550.199492 ETH
👤 Running casual player session: casual_1
🛒 Casual Spread Ammo: 750 ETH (ETH test mode)
💸 casual_1 sending 750 ETH to hot wallet for: Spread Ammo
🎮 Level 9: Simulating completion...
✅ Level 9 completed! PARTIAL: 1797 tokens
🎁 Game awarded 1797 WISH → 179.70000000000002 ETH
💰 Sending 179.70000000000002 ETH to grinder_2 for: Level 9 completion - game awarded 1797 WISH
🔢 Next nonce for casual_1: 0
⚠️ grinder_2: Rate limited (429) for http://localhost:3001/api/wallet/send, retrying in 49000ms
🏆 Grinder grinder_1 completed 10 levels with REAL rewards
✅ REAL blockchain transaction mined: 0x53ead27643dfad9af00188b135e0ad25d3bcb33f4ea6e1882ce50ae74340b9b3
⛽ Gas used: 21000
💸 casual_1 completed REAL ETH transfer: 750 ETH to hot wallet
🎮 Level 1: Simulating moderate completion (60%)...
✅ Level 1 completed! PARTIAL: 765 tokens
🎁 Game awarded 765 WISH → 76.5 ETH
💰 Sending 76.5 ETH to casual_1 for: Level 1 completion
⚠️ casual_1: Rate limited (429) for http://localhost:3001/api/wallet/send, retrying in 49000ms
🎮 Level 9: Simulating completion...
🚫 Level 9: No reward (level_already_completed_today)
💰 Balance change: -216.100150 ETH (14400.199492 ETH total)
📉 Treasury outflow detected: -216.100150 ETH
🎮 Level 9: Simulating completion...
✅ Level 9 completed! PARTIAL: 1159 tokens
🎁 Game awarded 1159 WISH → 115.9 ETH
💰 Sending 115.9 ETH to grinder_3 for: Level 9 completion - game awarded 1159 WISH
🎮 Level 9: Simulating completion...
🚫 Level 9: No reward (level_already_completed_today)
🎮 Level 10: Simulating completion...
✅ Level 10 completed! PARTIAL: 1249 tokens
🎁 Game awarded 1249 WISH → 124.9 ETH
💰 Sending 124.9 ETH to grinder_2 for: Level 10 completion - game awarded 1249 WISH
🎮 Level 10: Simulating completion...
✅ Level 10 completed! PARTIAL: 1814 tokens
🎁 Game awarded 1814 WISH → 181.4 ETH
💰 Sending 181.4 ETH to grinder_3 for: Level 10 completion - game awarded 1814 WISH
✅ API Call: POST /wallet/send (49181ms)
✅ REAL ETH transfer completed: 179.70000000000002 ETH to ******************************************
📤 Transaction hash: 0x7b2d8c6676ef412239a02f0d2925d57c78f67347b87f3e945af86f2b58387ea0
✅ API Call: POST /wallet/send (49156ms)
✅ REAL ETH transfer completed: 76.5 ETH to ******************************************
📤 Transaction hash: 0xafc82be7f4d73a957453ed514870b2f42f8a11cc8378f2d16ea2751f229ac800
✅ API Call: POST /wallet/send (48625ms)
✅ REAL ETH transfer completed: 115.9 ETH to ******************************************
📤 Transaction hash: 0x7324d3375eadd01f100f2157cbf329be10fb8cb04f3f62b2618ca5f60aa984d9
✅ API Call: POST /wallet/send (47906ms)
✅ REAL ETH transfer completed: 124.9 ETH to ******************************************
📤 Transaction hash: 0x834eb469ee0f64d4af7959bb293b42792171d920618872a6383b13e78f26dd91
🎮 Level 2: Simulating moderate completion (60%)...
✅ Level 2 completed! PARTIAL: 519 tokens
🎁 Game awarded 519 WISH → 51.900000000000006 ETH
💰 Sending 51.900000000000006 ETH to casual_1 for: Level 2 completion
✅ API Call: POST /wallet/send (47972ms)
✅ REAL ETH transfer completed: 181.4 ETH to ******************************************
📤 Transaction hash: 0x1ce2c471ac4a2eb703bccd321c111438fe36ec3ccfe6d6f66a266b09b1f73ca1
✅ API Call: POST /wallet/send (145ms)
✅ REAL ETH transfer completed: 51.900000000000006 ETH to ******************************************
📤 Transaction hash: 0x874a31fa96eec234e6816cc227775b87e9e4e7f525bf0597a0f2ca52d4d1c1f2
🎮 Level 10: Simulating completion...
🚫 Level 10: No reward (level_already_completed_today)
🎮 Level 10: Simulating completion...
🚫 Level 10: No reward (level_already_completed_today)
💰 Balance change: -730.300127 ETH (13669.899365 ETH total)
📉 Treasury outflow detected: -730.300127 ETH
🏆 Grinder grinder_2 completed 10 levels with REAL rewards
🎮 Level 3: Simulating moderate completion (60%)...
✅ Level 3 completed! JACKPOT: NaN tokens
🚫 Level 3: No reward (game decision)
🏆 Grinder grinder_3 completed 10 levels with REAL rewards
📊 Treasury Drain Test Results:
   Stress Duration: 65509ms
   Users Participating: 10
   Initial Balance: 100 ETH
   Final Balance: 13669.899364707713 ETH
   Total Drain: -13569.899365 ETH
   Drain Percentage: -13569.90%
   Treasury Survived: ✅ YES
🎮 Level 4: Simulating moderate completion (60%)...
✅ Level 4 completed! PARTIAL: NaN tokens
🚫 Level 4: No reward (game decision)
🏆 Grinder grinder_3 completed 10 levels with REAL rewards
✅ Completed grinder session for grinder_3
🏆 Grinder grinder_2 completed 10 levels with REAL rewards
✅ Completed grinder session for grinder_2
🎮 Level 5: Simulating moderate completion (60%)...
✅ Level 5 completed! PARTIAL: NaN tokens
🚫 Level 5: No reward (game decision)
📊 Multi-Account Coordination Results:
   Coordination Duration: 67624ms
   Accounts Coordinated: 5
   Treasury Impact: Analyzing...
🎮 Level 6: Simulating moderate completion (60%)...
✅ Level 6 completed! PARTIAL: NaN tokens
🚫 Level 6: No reward (game decision)
🎮 Level 7: Simulating moderate completion (60%)...
✅ Level 7 completed! PARTIAL: NaN tokens
🚫 Level 7: No reward (game decision)
🎯 Casual player casual_1 completed session
🎯 Starting grinder session: grinder_2
⏳ Grinder grinder_2 waiting 3000ms to prevent server overload...
🎮 Level 1: Simulating completion...
✅ Level 1 completed! PARTIAL: 482 tokens
🎁 Game awarded 482 WISH → 48.2 ETH
💰 Sending 48.2 ETH to grinder_2 for: Level 1 completion - game awarded 482 WISH
✅ API Call: POST /wallet/send (176ms)
✅ REAL ETH transfer completed: 48.2 ETH to ******************************************
📤 Transaction hash: 0x8c2c58a5f2016d3d6f87ec497ae8184c12510642348a474fedb05f5b6d156d58
💰 Balance change: -48.200021 ETH (13621.699344 ETH total)
📉 Treasury outflow detected: -48.200021 ETH
🎮 Level 2: Simulating completion...
✅ Level 2 completed! PARTIAL: 901 tokens
🎁 Game awarded 901 WISH → 90.10000000000001 ETH
💰 Sending 90.10000000000001 ETH to grinder_2 for: Level 2 completion - game awarded 901 WISH
✅ API Call: POST /wallet/send (173ms)
✅ REAL ETH transfer completed: 90.10000000000001 ETH to ******************************************
📤 Transaction hash: 0x643b897283811886c86759f0320561fb6afcb16487fa335481740e1fe03d8437
🎮 Level 3: Simulating completion...
✅ Level 3 completed! PARTIAL: 572 tokens
🎁 Game awarded 572 WISH → 57.2 ETH
💰 Sending 57.2 ETH to grinder_2 for: Level 3 completion - game awarded 572 WISH
✅ API Call: POST /wallet/send (165ms)
✅ REAL ETH transfer completed: 57.2 ETH to ******************************************
📤 Transaction hash: 0x0e4072af5fc78c842d45adfc5f2eb126a2d5979cdf1f48cad53aceac5d8c8225
🎮 Level 4: Simulating completion...
✅ Level 4 completed! PARTIAL: 571 tokens
🎁 Game awarded 571 WISH → 57.1 ETH
💰 Sending 57.1 ETH to grinder_2 for: Level 4 completion - game awarded 571 WISH
✅ API Call: POST /wallet/send (183ms)
✅ REAL ETH transfer completed: 57.1 ETH to ******************************************
📤 Transaction hash: 0xf59bcae3f51d58987e0ae6d34067a535804c34358c43c32815cf0b3e51c8908f
🎮 Level 5: Simulating completion...
✅ Level 5 completed! PARTIAL: 1334 tokens
🎁 Game awarded 1334 WISH → 133.4 ETH
💰 Sending 133.4 ETH to grinder_2 for: Level 5 completion - game awarded 1334 WISH
✅ API Call: POST /wallet/send (189ms)
✅ REAL ETH transfer completed: 133.4 ETH to ******************************************
📤 Transaction hash: 0x39de901270875d40b7b1158d9316986a09702da57a7c66310fef29f355849e47
💰 Balance change: -337.800084 ETH (13283.899259 ETH total)
📉 Treasury outflow detected: -337.800084 ETH
🎮 Level 6: Simulating completion...
✅ Level 6 completed! PARTIAL: 1909 tokens
🎁 Game awarded 1909 WISH → 190.9 ETH
💰 Sending 190.9 ETH to grinder_2 for: Level 6 completion - game awarded 1909 WISH
✅ API Call: POST /wallet/send (172ms)
✅ REAL ETH transfer completed: 190.9 ETH to ******************************************
📤 Transaction hash: 0x86c696aa0a5689ac4e39fb8a74b17dfd8b7a98cf48de76cfc9fc034c443d9855
🎮 Level 7: Simulating completion...
✅ Level 7 completed! PARTIAL: 1516 tokens
🎁 Game awarded 1516 WISH → 151.6 ETH
💰 Sending 151.6 ETH to grinder_2 for: Level 7 completion - game awarded 1516 WISH
✅ API Call: POST /wallet/send (169ms)
✅ REAL ETH transfer completed: 151.6 ETH to ******************************************
📤 Transaction hash: 0x329e6de776f1030ac0dec930cb28b3e38ba2508baa20f68c568231d2401ecb98
🎮 Level 8: Simulating completion...
✅ Level 8 completed! PARTIAL: 1110 tokens
🎁 Game awarded 1110 WISH → 111 ETH
💰 Sending 111 ETH to grinder_2 for: Level 8 completion - game awarded 1110 WISH
✅ API Call: POST /wallet/send (237ms)
✅ REAL ETH transfer completed: 111 ETH to ******************************************
📤 Transaction hash: 0x594da135e016b372f53e547daa2ff0c9d9d3a347adb2aedb5cc110f677652513
💰 Balance change: -453.500063 ETH (12830.399196 ETH total)
📉 Treasury outflow detected: -453.500063 ETH
🎮 Level 9: Simulating completion...
✅ Level 9 completed! PARTIAL: 1650 tokens
🎁 Game awarded 1650 WISH → 165 ETH
💰 Sending 165 ETH to grinder_2 for: Level 9 completion - game awarded 1650 WISH
✅ API Call: POST /wallet/send (164ms)
✅ REAL ETH transfer completed: 165 ETH to ******************************************
📤 Transaction hash: 0x17e2b094d1cbcadf2a3f4c33558b4f7d677115598ca153af6845fcb83cd4170a
🎮 Level 10: Simulating completion...
✅ Level 10 completed! PARTIAL: 1957 tokens
🎁 Game awarded 1957 WISH → 195.70000000000002 ETH
💰 Sending 195.70000000000002 ETH to grinder_2 for: Level 10 completion - game awarded 1957 WISH
✅ API Call: POST /wallet/send (177ms)
✅ REAL ETH transfer completed: 195.70000000000002 ETH to ******************************************
📤 Transaction hash: 0xb6837833432d446930ee104393edd4478361f088be569e093e746b8ac2e4f3f8
🏆 Grinder grinder_2 completed 10 levels with REAL rewards
📊 Treasury impact after grinder_2: -12369.699154 ETH
👤 Running casual player session: casual_2
🎮 Level 1: Simulating moderate completion (60%)...
✅ Level 1 completed! JACKPOT: NaN tokens
🚫 Level 1: No reward (game decision)
🎮 Level 2: Simulating moderate completion (60%)...
✅ Level 2 completed! PARTIAL: NaN tokens
🚫 Level 2: No reward (game decision)
🎮 Level 3: Simulating moderate completion (60%)...
✅ Level 3 completed! PARTIAL: NaN tokens
🚫 Level 3: No reward (game decision)
💰 Balance change: -360.700042 ETH (12469.699154 ETH total)
📉 Treasury outflow detected: -360.700042 ETH
🎮 Level 4: Simulating moderate completion (60%)...
✅ Level 4 completed! PARTIAL: NaN tokens
🚫 Level 4: No reward (game decision)
🎮 Level 5: Simulating moderate completion (60%)...
✅ Level 5 completed! JACKPOT: NaN tokens
🚫 Level 5: No reward (game decision)
🎮 Level 6: Simulating moderate completion (60%)...
✅ Level 6 completed! PARTIAL: NaN tokens
🚫 Level 6: No reward (game decision)
🎮 Level 7: Simulating moderate completion (60%)...
✅ Level 7 completed! PARTIAL: NaN tokens
🚫 Level 7: No reward (game decision)
🎯 Casual player casual_2 completed session
🎯 Starting grinder session: grinder_3
⏳ Grinder grinder_3 waiting 4500ms to prevent server overload...
🎮 Level 1: Simulating completion...
✅ Level 1 completed! PARTIAL: 855 tokens
🎁 Game awarded 855 WISH → 85.5 ETH
💰 Sending 85.5 ETH to grinder_3 for: Level 1 completion - game awarded 855 WISH
✅ API Call: POST /wallet/send (168ms)
✅ REAL ETH transfer completed: 85.5 ETH to ******************************************
📤 Transaction hash: 0xd70bdcd726685a52f14df9ff1ebb1b840a2484db625d4ebe17da9cca45536238
🎮 Level 2: Simulating completion...
✅ Level 2 completed! PARTIAL: 898 tokens
🎁 Game awarded 898 WISH → 89.80000000000001 ETH
💰 Sending 89.80000000000001 ETH to grinder_3 for: Level 2 completion - game awarded 898 WISH
✅ API Call: POST /wallet/send (159ms)
✅ REAL ETH transfer completed: 89.80000000000001 ETH to ******************************************
📤 Transaction hash: 0x292e9d1be1a826c2ca59ccd45c5764d415453d1b9af2a80a4ed010e6e5ed0af0
🎮 Level 3: Simulating completion...
✅ Level 3 completed! PARTIAL: 651 tokens
🎁 Game awarded 651 WISH → 65.10000000000001 ETH
💰 Sending 65.10000000000001 ETH to grinder_3 for: Level 3 completion - game awarded 651 WISH
✅ API Call: POST /wallet/send (167ms)
✅ REAL ETH transfer completed: 65.10000000000001 ETH to ******************************************
📤 Transaction hash: 0x131f56341947079547f386f35b6cfbfa4ecbed7e8f3ef2fb76b2b4fab49374ce
🎮 Level 4: Simulating completion...
✅ Level 4 completed! PARTIAL: 715 tokens
🎁 Game awarded 715 WISH → 71.5 ETH
💰 Sending 71.5 ETH to grinder_3 for: Level 4 completion - game awarded 715 WISH
✅ API Call: POST /wallet/send (166ms)
✅ REAL ETH transfer completed: 71.5 ETH to ******************************************
📤 Transaction hash: 0xfc6fc128c1c665cf3df12b6d51c450601fbbf6897ecffe946234d7611873f376
💰 Balance change: -311.900084 ETH (12157.799070 ETH total)
📉 Treasury outflow detected: -311.900084 ETH
🎮 Level 5: Simulating completion...
✅ Level 5 completed! JACKPOT: NaN tokens
🚫 Level 5: No reward (game decision)
🎮 Level 6: Simulating completion...
✅ Level 6 completed! JACKPOT: NaN tokens
🚫 Level 6: No reward (game decision)
🎮 Level 7: Simulating completion...
✅ Level 7 completed! PARTIAL: NaN tokens
🚫 Level 7: No reward (game decision)
🎮 Level 8: Simulating completion...
✅ Level 8 completed! PARTIAL: NaN tokens
🚫 Level 8: No reward (game decision)
🎮 Level 9: Simulating completion...
✅ Level 9 completed! PARTIAL: NaN tokens
🚫 Level 9: No reward (game decision)
🎮 Level 10: Simulating completion...
✅ Level 10 completed! PARTIAL: NaN tokens
🚫 Level 10: No reward (game decision)
🏆 Grinder grinder_3 completed 10 levels with REAL rewards
📊 Treasury impact after grinder_3: -12057.799070 ETH
📈 Sequential Grinding Test Results:
   Initial Balance: 100 ETH
   Final Balance: 12157.799070021169 ETH
   Total Impact: -12057.799070 ETH
   Treasury Sustainable: ✅ YES
🛑 TreasuryMonitor stopped
📊 Monitored for 114660ms
💰 Final balance: 12157.799070021169 ETH
📈 Balance change: 12057.799070 ETH
🛑 TransactionTracker stopped
📊 Tracked 4 transactions in 114660ms
📊 Monitoring stopped
🔍 Queue health monitoring stopped
🔍 Running comprehensive validation...
🔍 Starting comprehensive validation...
💰 Validating treasury sustainability...
🔍 Treasury Status Debug:
🎨 Validating creator reward distribution...
🔍 Creator Analysis Debug:
🎮 Validating grinder behavior...
🔍 User Activity Debug:
🔗 Validating transaction integrity...
🔍 Transaction Data Debug:
⚖️ Validating economic balance...
🔧 Validating system stability...

🔍 VALIDATION RESULTS
==================================================
Overall Result: ❌ FAIL
Tests Passed: 12/17 (70.6%)
Critical Issues: 1
Warnings: 3

❌ CRITICAL ISSUES:
   1. Treasury Balance Change Reasonable: 12057.80% (expected: ≤ 50%)

⚠️ WARNINGS:
   1. Creator Rewards Distributed: 0 ETH (expected: > 0 ETH)
   2. Grinder Users Present: 0 grinders (expected: > 0 grinders (when user activity is tracked))
   3. Average Response Time Reasonable: 5666.090909090909ms (expected: ≤ 5000ms)

💡 RECOMMENDATIONS:
   1. CRITICAL: Address all critical issues before production deployment
   2. Increase initial treasury funding or reduce reward payouts
   3. Review warning items for potential improvements

✅ Validation completed
📊 Test Report Generated
🧹 Cleaning up rate limiter resources...
📊 Final rate limiter status: {}
✅ Rate limiter cleanup completed
✅ Stress test completed
🧹 Cleaning up rate limiter resources...
📊 Final rate limiter status: {}
✅ Rate limiter cleanup completed

📊 STRESS TEST RESULTS
============================================================
📋 Test Summary:
   Total Duration: 118.692 seconds
   Users Simulated: 10
   Total Transactions: 4
   Test Completion: Early completion

💰 Treasury Analysis:
   Initial Balance: 100 ETH
   Final Balance: 12157.799070021169 ETH
   Balance Change: 12057.799070021169 ETH
   Net Flow: 12057.799070021169 ETH
   Risk Level: low

🎨 Creator Reward Analysis:
   Total Creator Rewards: 0 ETH
   Average Reward per Environment: 0 ETH
   Reward Distribution Accuracy: 100%

📈 Sustainability Assessment:
   Is Sustainable: ✅ YES
   Risk Level: low
   Projected Runtime: undefined seconds

💡 Recommendations:
   1. Treasury balance stable
   2. Current tokenomics parameters appear sustainable
   3. Positive net flow - treasury is growing

✅ Stress test completed successfully
⏰ Test timeout reached