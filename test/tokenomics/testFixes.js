#!/usr/bin/env node

/**
 * Quick test to verify the fixes for the tokenomics issues
 */

import { TokenomicsStressTest } from './TokenomicsStressTest.js';

async function testFixes() {
    console.log('🧪 Testing Tokenomics Fixes');
    console.log('============================');
    
    const config = {
        apiBaseUrl: 'http://localhost:3001/api',
        hardhatUrl: 'http://localhost:8545',
        chainId: 31337,
        hotWalletAddress: '******************************************',
        testDuration: 60000, // 1 minute test
        maxConcurrentUsers: 3, // Small test
        verbose: true,
        scenario: 'grinder' // Focus on grinder behavior
    };

    try {
        const stressTest = new TokenomicsStressTest(config);
        
        console.log('🚀 Initializing test framework...');
        await stressTest.initialize();
        
        console.log('🎯 Running focused grinder test...');
        const report = await stressTest.runStressTest();
        
        console.log('✅ Test completed successfully!');
        console.log('📊 Final Report:', JSON.stringify(report, null, 2));
        
        return true;
    } catch (error) {
        console.error('❌ Test failed:', error);
        return false;
    }
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    testFixes()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('❌ Unexpected error:', error);
            process.exit(1);
        });
}

export { testFixes };
