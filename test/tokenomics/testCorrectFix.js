#!/usr/bin/env node

/**
 * Test to verify the correct fix - that we're calling the game's level completion system
 * instead of duplicating its logic in the test
 */

import { UserSimulator } from './UserSimulator.js';

async function testCorrectFix() {
    console.log('🧪 Testing Correct Fix - Game Level Completion System');
    console.log('====================================================');
    
    // Create a mock account
    const mockAccount = {
        address: '******************************************',
        privateKey: '0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80'
    };
    
    const config = {
        apiBaseUrl: 'http://localhost:3001/api',
        hardhatUrl: 'http://localhost:8545',
        chainId: 31337,
        hotWalletAddress: '******************************************'
    };

    try {
        // Create a grinder user simulator
        const grinder = new UserSimulator({
            type: 'grinder',
            account: mockAccount,
            config: config,
            id: 'test_grinder_1',
            transactionTracker: null,
            treasuryMonitor: null,
            globalRateLimiter: null
        });

        console.log('🎯 Testing game level completion system...');
        
        // Test multiple level completions to see daily limit behavior
        for (let level = 1; level <= 10; level++) {
            console.log(`\n--- Testing Level ${level} ---`);
            
            // Simulate level completion data (what the game would generate)
            const completionData = {
                levelNumber: level,
                completed: true,
                completionTime: 30 + Math.random() * 20,
                score: { totalScore: 1000 + Math.random() * 500 },
                enemiesDefeated: 50, // Perfect completion
                totalEnemies: 50,
                perfectCompletion: true,
                bonuses: { speed: true, accuracy: true, perfect: true },
                nextLevel: level + 1
            };

            console.log(`Calling game's handleLevelCompletion()...`);
            
            // Call the GAME's level completion handler (this is the correct approach)
            const result = await grinder.tokenEconomyManager.handleLevelCompletion(
                completionData,
                completionData.score.totalScore,
                { totalEnemies: 50 } // levelConfig
            );
            
            console.log(`Game result: success=${result.success}, tokensAwarded=${result.tokensAwarded || 0}`);
            console.log(`Reason: ${result.reason || 'success'}`);
            
            if (result.success && result.tokensAwarded > 0) {
                console.log(`✅ Level ${level}: Game awarded ${result.tokensAwarded} WISH tokens`);
                console.log(`   💰 Would send ${result.tokensAwarded * 0.1} ETH to user`);
                // In real test, we would call: await grinder.sendETHReward(ethAmount, reason);
            } else {
                console.log(`🚫 Level ${level}: No reward from game (${result.reason})`);
                console.log(`   ✅ CORRECT: No API call should be made!`);
            }
            
            // Check daily progress
            const progress = grinder.tokenEconomyManager.dailyRewardTracker.getDailyProgress();
            console.log(`Daily progress: ${progress.dailyTokensEarned}/${progress.maxDailyTokens} tokens`);
            
            if (progress.isLimitReached) {
                console.log(`🛑 Daily limit reached! Game should stop awarding tokens.`);
            }
        }
        
        console.log('\n✅ Test completed successfully!');
        console.log('📋 Key Points:');
        console.log('   - Test calls game\'s handleLevelCompletion() method');
        console.log('   - Game handles daily limits internally');
        console.log('   - Test only sends ETH if game awards tokens');
        console.log('   - No duplicate logic in test code');
        
        return true;
    } catch (error) {
        console.error('❌ Test failed:', error);
        return false;
    }
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    testCorrectFix()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('❌ Unexpected error:', error);
            process.exit(1);
        });
}

export { testCorrectFix };
