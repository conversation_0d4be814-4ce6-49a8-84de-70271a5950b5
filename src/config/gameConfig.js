/**
 * Game configuration constants
 */
export const GAME_CONFIG = {
    // Canvas settings
    CANVAS_WIDTH: 800,
    CANVAS_HEIGHT: 600,
    TARGET_FPS: 60,
    
    // Game mechanics
    PLAYER_LIVES: 3,
    PLAYER_HEALTH: 100,
    
    // Token economy
    BASE_LEVEL_REWARD: 1250,
    POWER_UP_COSTS: {
        EXTRA_WINGMAN: 10000,
        EXTRA_LIFE: 15000,
        SPREAD_AMMO: 7500,
        REALITY_WARP: 25000
    },

    // Probabilistic reward system - bell curve distribution with max reward as true maximum
    REWARD_SYSTEM: {
        // Variance system - perfect score has higher chance of max reward but doesn't guarantee it
        BASE_VARIANCE: 0.4,             // 40% variance from max (e.g., 750-1250 range)
        PERFECT_SCORE_VARIANCE: 0.2,    // Perfect score reduces variance to 20%
        POOR_SCORE_VARIANCE: 0.6,       // Poor performance increases variance to 60%

        // Power-up benefits - reduce variance (more consistent rewards)
        POWER_UP_VARIANCE_REDUCTION: 0.1, // Each power-up reduces variance by 10%
        MAX_VARIANCE_REDUCTION: 0.3,      // Maximum 30% variance reduction from power-ups

        // House edge - expected value is always less than max
        HOUSE_EDGE: 0.15,               // Expected reward is 85% of theoretical max
        MIN_REWARD_RATIO: 0.3,          // Minimum reward is 30% of max possible

        // Probabilistic distribution - bell curve favoring middle rewards
        MAX_REWARD_CHANCE: 0.10,        // 10% chance of getting max reward
        ABOVE_AVERAGE_CHANCE: 0.25,     // 25% chance of getting 75-99% of max
        AVERAGE_CHANCE: 0.40,           // 40% chance of getting 50-75% of max
        BELOW_AVERAGE_CHANCE: 0.20,     // 20% chance of getting 30-50% of max
        MIN_REWARD_CHANCE: 0.05         // 5% chance of getting minimum reward
    },

    // Reality warp costs
    WARP_BASE_COST: 20000,

    // Raffle system
    RAFFLE_PRIZE_POOL_PERCENTAGE: 0.5,
    RAFFLE_PRIZES: {
        GOLD: 25000,
        SILVER: 15000,
        BRONZE: 10000
    },
    
    // Development settings
    DEBUG_MODE: true,
    ENABLE_CONSOLE_LOGS: true
};

export const ENVIRONMENT_TYPES = {
    SPACE: 'space',
    WATER: 'water',
    FIRE: 'fire',
    AIR: 'air',
    EARTH: 'earth',
    CRYSTAL: 'crystal',
    SHADOW: 'shadow'
};

export const ENEMY_TYPES = {
    WATER: 'water',
    FIRE: 'fire',
    AIR: 'air',
    EARTH: 'earth',
    CRYSTAL: 'crystal',
    SHADOW: 'shadow'
};